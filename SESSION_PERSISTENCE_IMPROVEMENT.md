# Session Persistence Improvement

## Problem Analysis

Based on the log file analysis, the current session persistence implementation has several issues:

### Current Problems

1. **Complex State Management**: Multiple layers of session tracking
   - `contextTracker` object with sessionIds Map
   - MCP service session registry
   - Pattern matching to extract session IDs from text
   - Multiple fallback mechanisms

2. **Session Loss**: Sessions get lost between conversation continuations
   - Example from log: "Session not found: 2becbdb8-ed31-491e-97df-5eeeaa640ee2"
   - System creates new sessions repeatedly instead of reusing existing ones

3. **Variable Dependencies**: System relies on maintaining variables like:
   - `sessionIds` Map
   - `createdFiles` array
   - `recentOperations` array
   - `lastMentionedFile` string

## Simplified Solution: Transfer Data Through Chat History

Instead of maintaining complex state variables, we now embed all necessary context directly in the LLM conversation history.

### Key Changes Made

1. **Replaced Complex Context Tracker**:
   ```typescript
   // OLD: Complex state management
   const contextTracker = {
     createdFiles: [] as string[],
     sessionIds: new Map<string, string>(),
     lastMentionedFile: null as string | null,
     recentOperations: [] as Array<{ tool: string; operation: string; timestamp: number }>
   }

   // NEW: Simple extraction from chat history
   const extractContextFromHistory = (history) => {
     const context = {
       activeSessions: new Map<string, string>(),
       recentFiles: [] as string[],
       lastSessionCreated: null as string | null
     }
     // Extract from most recent messages only
   }
   ```

2. **Context Extraction from Recent Messages**:
   - Only looks at last 10 messages for efficiency
   - Extracts session IDs from assistant responses and tool results
   - Finds file paths mentioned in conversation
   - No persistent variables to maintain

3. **Simplified System Prompt Building**:
   - Uses extracted context directly
   - No complex tracking or updating
   - Context is always fresh from actual conversation

### How It Works

1. **Session Context in Messages**: When a session is created, the session ID appears in the assistant's response message content (e.g., "Session ID: 2becbdb8-ed31-491e-97df-5eeeaa640ee2")

2. **Context Extraction**: Before each LLM call, the system:
   - Scans the last 10 messages in conversation history
   - Extracts session IDs using regex patterns
   - Finds file paths mentioned in content
   - Builds context map from this data

3. **System Prompt Enhancement**: The extracted context is added to the system prompt:
   ```
   CURRENT CONTEXT:
   Active sessions:
   - Headless Terminal: 2becbdb8-ed31-491e-97df-5eeeaa640ee2

   Recently created/mentioned files:
   - ~/Desktop/current_time.txt
   ```

### Benefits

1. **No State Variables**: All context comes from conversation history
2. **Session Persistence**: Sessions persist across conversation continuations because they're in the chat history
3. **Simplicity**: Much simpler implementation with fewer failure points
4. **Reliability**: Context is always accurate because it's extracted from actual conversation
5. **Efficiency**: Only processes recent messages, not entire conversation history

### Example Flow

1. User: "make a file on my desktop with the current time"
2. Assistant creates session, response includes: "Session ID: abc123-def456"
3. User continues conversation: "read the file we just made"
4. System extracts "abc123-def456" from previous messages
5. System adds to prompt: "Active sessions: - Headless Terminal: abc123-def456"
6. Assistant uses existing session instead of creating new one

This approach eliminates the session loss problem seen in the log file and makes the system much more reliable for conversation continuations.

## Additional Recommendations

### 1. Enhanced Session Context in Assistant Responses

To make session persistence even more reliable, consider having the assistant include session context in its responses:

```typescript
// When creating a session, include context in response
"I've created a new terminal session (ID: abc123-def456) and will use this for subsequent commands."

// When using existing session, reference it
"Using existing terminal session abc123-def456 to read the file."
```

### 2. Session Validation

Add session validation to prevent using stale sessions:

```typescript
const isSessionStillActive = async (sessionId: string) => {
  try {
    // Try a simple command to test if session is alive
    await mcpService.callTool('ht_take_snapshot', { sessionId })
    return true
  } catch (error) {
    return false
  }
}
```

### 3. Context Persistence in Conversation Storage

Store extracted context in conversation metadata for faster access:

```typescript
interface ConversationMetadata {
  lastActiveSession?: string
  recentFiles?: string[]
  lastUpdated?: number
}
```

### 4. Fallback Strategy

Implement a fallback strategy when sessions are lost:

```typescript
// If extracted session is invalid, create new one and inform user
"The previous terminal session is no longer available. I'll create a new session to continue."
```

These improvements would make the session persistence system even more robust and user-friendly.
